{"name": "tanstarter", "private": true, "type": "module", "scripts": {"deploy": " wrangler deploy", "cf-typegen": "wrangler types --env-interface Env", "dev": "vite dev --port 3000", "build": "vite build", "start": "node .output/server/index.mjs", "lint": "eslint .", "format": "prettier --write .", "check-types": "tsc --noEmit", "check": "pnpm format && pnpm lint && pnpm check-types", "db": "drizzle-kit", "deps": "pnpm dlx taze@latest major -Ilw", "ui": "pnpm dlx shadcn@latest", "auth:secret": "pnpm dlx @better-auth/cli@latest secret", "auth:generate": "pnpm dlx @better-auth/cli@latest generate --config ./src/lib/auth/index.ts --y --output ./src/lib/db/schema/auth.schema.ts && prettier --write ./src/lib/db/schema/auth.schema.ts"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@t3-oss/env-core": "^0.13.8", "@tanstack/react-query": "^5.82.0", "@tanstack/react-query-devtools": "^5.82.0", "@tanstack/react-router": "^1.125.6", "@tanstack/react-router-devtools": "^1.125.6", "@tanstack/react-router-with-query": "^1.125.6", "@tanstack/react-start": "^1.126.0", "@vitejs/plugin-react": "^4.6.0", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.5", "lucide-react": "latest", "postgres": "^3.4.7", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "valibot": "^1.1.0", "vite": "^7.0.3", "wrangler": "^4.33.1", "zod": "^3.25.76"}, "devDependencies": {"@biomejs/biome": "2.2.2", "@eslint-react/eslint-plugin": "^1.52.2", "@eslint/js": "^9.30.1", "@tailwindcss/vite": "^4.1.11", "@tanstack/eslint-plugin-query": "^5.81.2", "@tanstack/eslint-plugin-router": "^1.125.0", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "babel-plugin-react-compiler": "latest", "drizzle-kit": "^0.31.4", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react-hooks": "rc", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "vite-tsconfig-paths": "^5.1.4"}}