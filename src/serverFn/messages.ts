import { createServerFn } from "@tanstack/react-start";
import { eq } from "drizzle-orm";
import { db } from "~/lib/db";
import { messages } from "~/lib/db/schema/schema";

// List all messages
export const listMessages = createServerFn({ method: "GET" }).handler(async () => {
  return db.select().from(messages);
});

// Get one message by id
export const getMessage = createServerFn({ method: "POST" }).handler(
  async (data: { id: string }) => {
    if (!data?.id) throw new Error("id is required");
    const [row] = await db.select().from(messages).where(eq(messages.id, data.id));
    return row ?? null;
  },
);

// Create a message
export const createMessage = createServerFn({ method: "POST" }).handler(
  async (data: { sectionId: string; content?: string | null }) => {
    if (!data?.sectionId) throw new Error("sectionId is required");
    const [created] = await db
      .insert(messages)
      .values({ sectionId: data.sectionId, content: data.content ?? null })
      .returning();
    return created;
  },
);

// Update a message
export const updateMessage = createServerFn({ method: "POST" }).handler(
  async (data: { id: string; content?: string | null }) => {
    if (!data?.id) throw new Error("id is required");
    const updateValues: Partial<typeof messages["$inferInsert"]> = {};
    if (typeof data.content !== "undefined") updateValues.content = data.content;

    if (Object.keys(updateValues).length === 0) {
      throw new Error("No fields provided to update");
    }

    const [updated] = await db
      .update(messages)
      .set(updateValues)
      .where(eq(messages.id, data.id))
      .returning();

    return updated ?? null;
  },
);

// Delete a message by id
export const deleteMessage = createServerFn({ method: "POST" }).handler(
  async (data: { id: string }) => {
    if (!data?.id) throw new Error("id is required");
    const [deleted] = await db.delete(messages).where(eq(messages.id, data.id)).returning();
    return deleted ?? null;
  },
);
