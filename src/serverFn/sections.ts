import { createServerFn } from "@tanstack/react-start";
import { eq } from "drizzle-orm";
import { db } from "~/lib/db";
import { messageSection } from "~/lib/db/schema/schema";

// List all sections
export const listSections = createServerFn({ method: "GET" }).handler(async () => {
  return db.select().from(messageSection);
});

// Get one section by id
export const getSection = createServerFn({ method: "POST" }).handler(
  async (data: { id: string }) => {
    if (!data?.id) throw new Error("id is required");
    const [row] = await db
      .select()
      .from(messageSection)
      .where(eq(messageSection.id, data.id));
    return row ?? null;
  },
);

// Create a section
export const createSection = createServerFn({ method: "POST" }).handler(
  async (data: { roomId: string; title: string }) => {
    if (!data?.roomId) throw new Error("roomId is required");
    if (!data?.title) throw new Error("title is required");
    const [created] = await db
      .insert(messageSection)
      .values({ roomId: data.roomId, title: data.title })
      .returning();
    return created;
  },
);

// Update a section
export const updateSection = createServerFn({ method: "POST" }).handler(
  async (data: { id: string; title?: string }) => {
    if (!data?.id) throw new Error("id is required");
    const updateValues: Partial<typeof messageSection["$inferInsert"]> = {};
    if (typeof data.title === "string") updateValues.title = data.title;

    if (Object.keys(updateValues).length === 0) {
      throw new Error("No fields provided to update");
    }

    const [updated] = await db
      .update(messageSection)
      .set(updateValues)
      .where(eq(messageSection.id, data.id))
      .returning();

    return updated ?? null;
  },
);

// Delete a section by id
export const deleteSection = createServerFn({ method: "POST" }).handler(
  async (data: { id: string }) => {
    if (!data?.id) throw new Error("id is required");
    const [deleted] = await db
      .delete(messageSection)
      .where(eq(messageSection.id, data.id))
      .returning();
    return deleted ?? null;
  },
);
