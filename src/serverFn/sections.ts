import { createServerFn } from "@tanstack/react-start";
import { eq } from "drizzle-orm";
import * as v from "valibot";
import { db } from "~/lib/db";
import { messageSections } from "~/lib/db/schema/schema";
import { SectionCreateSchema, SectionUpdateSchema } from "~/validation/schema";

// List all sections
export const listSections = createServerFn({ method: "GET" }).handler(async () => {
  return db.select().from(messageSections);
});

// Get one section by id
export const getSection = createServerFn({ method: "POST" }).handler(
  async (data: unknown) => {
    const validatedData = v.parse(v.object({ id: v.string() }), data);
    const [row] = await db
      .select()
      .from(messageSections)
      .where(eq(messageSections.id, validatedData.id));
    return row ?? null;
  },
);

// Create a section
export const createSection = createServerFn({ method: "POST" }).handler(
  async (data: unknown) => {
    const validatedData = v.parse(SectionCreateSchema, data);
    const [created] = await db
      .insert(messageSections)
      .values({ roomId: validatedData.roomId, title: validatedData.title })
      .returning();
    return created;
  },
);

// Update a section
export const updateSection = createServerFn({ method: "POST" }).handler(
  async (data: unknown) => {
    const validatedData = v.parse(SectionUpdateSchema, data);
    const updateValues: Partial<typeof messageSections["$inferInsert"]> = {};
    if (typeof validatedData.title === "string") updateValues.title = validatedData.title;

    if (Object.keys(updateValues).length === 0) {
      throw new Error("No fields provided to update");
    }

    const [updated] = await db
      .update(messageSections)
      .set(updateValues)
      .where(eq(messageSections.id, validatedData.id))
      .returning();

    return updated ?? null;
  },
);

// Delete a section by id
export const deleteSection = createServerFn({ method: "POST" }).handler(
  async (data: unknown) => {
    const validatedData = v.parse(v.object({ id: v.string() }), data);
    const [deleted] = await db
      .delete(messageSections)
      .where(eq(messageSections.id, validatedData.id))
      .returning();
    return deleted ?? null;
  },
);
