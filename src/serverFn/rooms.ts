import { createServerFn } from "@tanstack/react-start";
import { eq } from "drizzle-orm";
import { db } from "~/lib/db";
import { rooms } from "~/lib/db/schema/schema";

// List all rooms
export const listRooms = createServerFn({ method: "GET" }).handler(async () => {
  return db.select().from(rooms);
});

// Get one room by id
export const getRoom = createServerFn({ method: "POST" }).handler(
  async (data: { id: string }) => {
    if (!data?.id) throw new Error("id is required");
    const [row] = await db.select().from(rooms).where(eq(rooms.id, data.id));
    return row ?? null;
  },
);

// Create a room
export const createRoom = createServerFn({ method: "POST" }).handler(
  async (data: { name: string }) => {
    if (!data?.name) throw new Error("name is required");
    const [created] = await db
      .insert(rooms)
      .values({ name: data.name })
      .returning();
    return created;
  },
);

// Update a room
export const updateRoom = createServerFn({ method: "POST" }).handler(
  async (data: { id: string; name?: string }) => {
    if (!data?.id) throw new Error("id is required");
    const updateValues: Partial<typeof rooms["$inferInsert"]> = {};
    if (typeof data.name === "string") updateValues.name = data.name;

    if (Object.keys(updateValues).length === 0) {
      throw new Error("No fields provided to update");
    }

    const [updated] = await db
      .update(rooms)
      .set(updateValues)
      .where(eq(rooms.id, data.id))
      .returning();

    return updated ?? null;
  },
);

// Delete a room by id
export const deleteRoom = createServerFn({ method: "POST" }).handler(
  async (data: { id: string }) => {
    if (!data?.id) throw new Error("id is required");
    const [deleted] = await db.delete(rooms).where(eq(rooms.id, data.id)).returning();
    return deleted ?? null;
  },
);
