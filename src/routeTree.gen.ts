/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createServerRootRoute } from "@tanstack/react-start/server";

import { Route as rootRouteImport } from "./routes/__root";
import { Route as PassEverythingRouteRouteImport } from "./routes/pass-everything/route";
import { Route as IndexRouteImport } from "./routes/index";
import { ServerRoute as ApiAuthSplatServerRouteImport } from "./routes/api/auth/$";

const rootServerRouteImport = createServerRootRoute();

const PassEverythingRouteRoute = PassEverythingRouteRouteImport.update({
  id: "/pass-everything",
  path: "/pass-everything",
  getParentRoute: () => rootRouteImport,
} as any);
const IndexRoute = IndexRouteImport.update({
  id: "/",
  path: "/",
  getParentRoute: () => rootRouteImport,
} as any);
const ApiAuthSplatServerRoute = ApiAuthSplatServerRouteImport.update({
  id: "/api/auth/$",
  path: "/api/auth/$",
  getParentRoute: () => rootServerRouteImport,
} as any);

export interface FileRoutesByFullPath {
  "/": typeof IndexRoute;
  "/pass-everything": typeof PassEverythingRouteRoute;
}
export interface FileRoutesByTo {
  "/": typeof IndexRoute;
  "/pass-everything": typeof PassEverythingRouteRoute;
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport;
  "/": typeof IndexRoute;
  "/pass-everything": typeof PassEverythingRouteRoute;
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths: "/" | "/pass-everything";
  fileRoutesByTo: FileRoutesByTo;
  to: "/" | "/pass-everything";
  id: "__root__" | "/" | "/pass-everything";
  fileRoutesById: FileRoutesById;
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute;
  PassEverythingRouteRoute: typeof PassEverythingRouteRoute;
}
export interface FileServerRoutesByFullPath {
  "/api/auth/$": typeof ApiAuthSplatServerRoute;
}
export interface FileServerRoutesByTo {
  "/api/auth/$": typeof ApiAuthSplatServerRoute;
}
export interface FileServerRoutesById {
  __root__: typeof rootServerRouteImport;
  "/api/auth/$": typeof ApiAuthSplatServerRoute;
}
export interface FileServerRouteTypes {
  fileServerRoutesByFullPath: FileServerRoutesByFullPath;
  fullPaths: "/api/auth/$";
  fileServerRoutesByTo: FileServerRoutesByTo;
  to: "/api/auth/$";
  id: "__root__" | "/api/auth/$";
  fileServerRoutesById: FileServerRoutesById;
}
export interface RootServerRouteChildren {
  ApiAuthSplatServerRoute: typeof ApiAuthSplatServerRoute;
}

declare module "@tanstack/react-router" {
  interface FileRoutesByPath {
    "/pass-everything": {
      id: "/pass-everything";
      path: "/pass-everything";
      fullPath: "/pass-everything";
      preLoaderRoute: typeof PassEverythingRouteRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    "/": {
      id: "/";
      path: "/";
      fullPath: "/";
      preLoaderRoute: typeof IndexRouteImport;
      parentRoute: typeof rootRouteImport;
    };
  }
}
declare module "@tanstack/react-start/server" {
  interface ServerFileRoutesByPath {
    "/api/auth/$": {
      id: "/api/auth/$";
      path: "/api/auth/$";
      fullPath: "/api/auth/$";
      preLoaderRoute: typeof ApiAuthSplatServerRouteImport;
      parentRoute: typeof rootServerRouteImport;
    };
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  PassEverythingRouteRoute: PassEverythingRouteRoute,
};
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();
const rootServerRouteChildren: RootServerRouteChildren = {
  ApiAuthSplatServerRoute: ApiAuthSplatServerRoute,
};
export const serverRouteTree = rootServerRouteImport
  ._addFileChildren(rootServerRouteChildren)
  ._addFileTypes<FileServerRouteTypes>();
