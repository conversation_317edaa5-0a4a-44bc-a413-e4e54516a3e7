import * as v from "valibot";

export const MessageCreateSchema = v.object({
	roomId: v.string(),
	title: v.optional(v.string()),
	content: v.optional(v.string()),
});

export const MessageUpdateSchema = v.object({
	id: v.string(),
	title: v.optional(v.string()),
	content: v.optional(v.string()),
});

export const RoomCreateSchema = v.object({
	name: v.string(),
});

export const RoomUpdateSchema = v.object({
	id: v.string(),
	name: v.optional(v.string()),
});
