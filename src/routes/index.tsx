import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useState } from "react";
import { JoinRoomDialog } from "~/components/JoinRoomDialog";
import { Button } from "~/components/ui/button";

export const Route = createFileRoute("/")({
	component: Home,
	/* loader: ({ context }) => {
    return { user: context.user };
  },*/
});

function Home() {
	const navigate = useNavigate();

	const handleStart = () => {
		navigate({ to: "/pass-everything" });
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
			<div className="bg-white rounded-2xl shadow-xl p-8 w-full max-w-md">
				<div className="text-center mb-8">
					<h1 className="text-3xl font-bold text-gray-800 mb-2">Welcome</h1>
					<p className="text-gray-600">Choose how you'd like to get started</p>
				</div>

				<div className="space-y-4">
					<Button
						type={"button"}
						onClick={handleStart}
						className="cursor-pointer w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-6 rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg"
					>
						Start
					</Button>
					<JoinRoomDialog />
				</div>
			</div>
		</div>
	);
}
