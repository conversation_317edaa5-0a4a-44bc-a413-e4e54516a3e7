import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { ArrowLeft, Check, Copy, Share2, Users, Zap } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { Button } from "~/components/ui/button";
import { Label } from "~/components/ui/label";

export const Route = createFileRoute("/pass-everything")({
	component: TextSyncPage,
});

function TextSyncPage() {
	const navigate = useNavigate();
	const [text, setText] = useState("");
	const [roomCode, setRoomCode] = useState("");
	const [isConnected, setIsConnected] = useState(false);
	const [copied, setCopied] = useState(false);
	const textareaRef = useRef<HTMLTextAreaElement>(null);

	// Generate a random room code when component mounts
	useEffect(() => {
		const generateRoomCode = () => {
			const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
			let result = "";
			for (let i = 0; i < 6; i++) {
				result += chars.charAt(Math.floor(Math.random() * chars.length));
			}
			return result;
		};

		setRoomCode(generateRoomCode());
		setIsConnected(true);
	}, []);

	// Auto-resize textarea
	useEffect(() => {
		if (textareaRef.current) {
			textareaRef.current.style.height = "auto";
			textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
		}
	}, []);

	const handleCopyText = async () => {
		if (text) {
			try {
				await navigator.clipboard.writeText(text);
				setCopied(true);
				setTimeout(() => setCopied(false), 2000);
			} catch (err) {
				console.error("Failed to copy text:", err);
			}
		}
	};

	const handleCopyRoomCode = async () => {
		try {
			await navigator.clipboard.writeText(roomCode);
		} catch (err) {
			console.error("Failed to copy room code:", err);
		}
	};

	const handleShare = async () => {
		const shareData = {
			title: "Join my text sync session",
			text: `Join my PassEverything sync session with code: ${roomCode}`,
			url: window.location.href,
		};

		if (navigator.share) {
			try {
				await navigator.share(shareData);
			} catch (err) {
				console.error("Error sharing:", err);
			}
		} else {
			// Fallback to copying URL
			await navigator.clipboard.writeText(
				`${window.location.origin}?join=${roomCode}`,
			);
		}
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900">
			{/* Header */}
			<header className="p-4 border-b border-white/20 dark:border-gray-700/20 backdrop-blur-sm bg-white/80 dark:bg-gray-800/80">
				<div className="max-w-4xl mx-auto flex items-center justify-between">
					<div className="flex items-center gap-4">
						<Button
							variant="ghost"
							size="icon"
							onClick={() => navigate({ to: "/" })}
							className="hover:bg-white/50 dark:hover:bg-gray-700/50"
						>
							<ArrowLeft className="w-5 h-5" />
						</Button>
						<div className="flex items-center gap-2">
							<div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
								<Copy className="w-5 h-5 text-white" />
							</div>
							<span className="font-bold text-lg bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
								PassEverything
							</span>
						</div>
					</div>

					<div className="flex items-center gap-3">
						<div className="flex items-center gap-2 px-3 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-sm">
							<div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
							{isConnected ? "Connected" : "Connecting..."}
						</div>
					</div>
				</div>
			</header>

			<div className="max-w-4xl mx-auto p-6">
				{/* Room Info Card */}
				<div className="mb-6 bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-white/20 dark:border-gray-700/20">
					<div className="flex items-center justify-between mb-4">
						<div>
							<h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-1">
								Sync Session
							</h2>
							<p className="text-gray-600 dark:text-gray-300 text-sm">
								Share this code with other devices to sync text
							</p>
						</div>
						<div className="flex items-center gap-2">
							<Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
							<span className="text-sm text-gray-600 dark:text-gray-300">
								1 device
							</span>
						</div>
					</div>

					<div className="flex items-center gap-3">
						<div className="flex-1">
							<Label
								htmlFor="room-code"
								className="text-sm font-medium text-gray-700 dark:text-gray-300"
							>
								Session Code
							</Label>
							<div className="mt-1 flex items-center gap-2">
								<div className="px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg font-mono text-lg tracking-wider text-center min-w-[120px]">
									{roomCode}
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={handleCopyRoomCode}
									className="shrink-0"
								>
									<Copy className="w-4 h-4" />
								</Button>
								<Button
									variant="outline"
									size="sm"
									onClick={handleShare}
									className="shrink-0"
								>
									<Share2 className="w-4 h-4" />
								</Button>
							</div>
						</div>
					</div>
				</div>

				{/* Text Sync Area */}
				<div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/20 overflow-hidden">
					<div className="p-6 border-b border-gray-200 dark:border-gray-700">
						<div className="flex items-center justify-between">
							<div>
								<h3 className="text-lg font-semibold text-gray-900 dark:text-white">
									Synchronized Text
								</h3>
								<p className="text-gray-600 dark:text-gray-300 text-sm">
									Type or paste text here - it will sync across all connected
									devices
								</p>
							</div>
							<div className="flex items-center gap-2">
								<Zap className="w-5 h-5 text-yellow-500" />
								<span className="text-sm text-gray-600 dark:text-gray-300">
									Real-time
								</span>
							</div>
						</div>
					</div>

					<div className="p-6">
						<textarea
							ref={textareaRef}
							value={text}
							onChange={(e) => setText(e.target.value)}
							placeholder="Start typing or paste your text here..."
							className="w-full min-h-[300px] p-4 border border-gray-200 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all duration-200"
							style={{ height: "auto" }}
						/>

						<div className="flex items-center justify-between mt-4">
							<div className="text-sm text-gray-500 dark:text-gray-400">
								{text.length} characters
							</div>

							<Button
								onClick={handleCopyText}
								disabled={!text}
								className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
							>
								{copied ? (
									<>
										<Check className="w-4 h-4 mr-2" />
										Copied!
									</>
								) : (
									<>
										<Copy className="w-4 h-4 mr-2" />
										Copy Text
									</>
								)}
							</Button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
