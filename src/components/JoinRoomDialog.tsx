import { useState } from "react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";

export function JoinRoomDialog() {
	const [roomCode, setRoomCode] = useState("");
	const [isLoading, setIsLoading] = useState(false);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!roomCode.trim()) return;

		setIsLoading(true);
		// Simulate API call
		await new Promise((resolve) => setTimeout(resolve, 1000));
		setIsLoading(false);

		// Handle room joining logic here
		console.log("Joining room:", roomCode);
	};

	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button
					variant="outline"
					className="w-full border-2 border-dashed border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-all duration-200"
				>
					Join Existing Room
				</Button>
			</DialogTrigger>
			<DialogContent className="sm:max-w-[450px]">
				<form onSubmit={handleSubmit}>
					<DialogHeader>
						<DialogTitle>Join a Room</DialogTitle>
						<DialogDescription>
							Enter the room code to join an existing conversation. You can get
							this code from someone already in the room.
						</DialogDescription>
					</DialogHeader>
					<div className="grid gap-6 py-4">
						<div className="grid gap-3">
							<Label htmlFor="room-code" className="text-sm font-medium">
								Room Code
							</Label>
							<Input
								name="roomCode"
								placeholder="Enter room code (e.g., ABC123)"
								value={roomCode}
								onChange={(e) => setRoomCode(e.target.value)}
								className="text-center text-lg font-mono tracking-wider uppercase"
								maxLength={6}
								required
							/>
						</div>
					</div>
					<DialogFooter>
						<DialogClose asChild>
							<Button variant="outline" disabled={isLoading}>
								Cancel
							</Button>
						</DialogClose>
						<Button
							type="submit"
							disabled={!roomCode.trim() || isLoading}
							className="min-w-[100px]"
						>
							{isLoading ? "Joining..." : "Join Room"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
