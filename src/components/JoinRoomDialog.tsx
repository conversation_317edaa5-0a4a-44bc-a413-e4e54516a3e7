import { useState } from "react";
import { useNavigate } from "@tanstack/react-router";
import { Button } from "~/components/ui/button";
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	<PERSON>alogHeader,
	<PERSON>alogTitle,
	DialogTrigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { safeParse } from "~/validation";
import { JoinRoomSchema } from "~/validation/schema";

export function JoinRoomDialog() {
	const navigate = useNavigate();
	const [roomCode, setRoomCode] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const [isOpen, setIsOpen] = useState(false);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!roomCode.trim()) return;

		setError("");
		setIsLoading(true);

		// Validate room code format
		const validation = safeParse(JoinRoomSchema, { code: roomCode.toUpperCase() });

		if (!validation.success) {
			setError("Please enter a valid 6-character room code");
			setIsLoading(false);
			return;
		}

		try {
			// Simulate API call to check if room exists
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// Navigate to the sync page with the room code
			navigate({
				to: "/pass-everything",
				search: { join: validation.data.code }
			});

			setIsOpen(false);
		} catch (err) {
			setError("Failed to join session. Please try again.");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={setIsOpen}>
			<DialogTrigger asChild>
				<Button
					variant="outline"
					className="w-full border-2 border-dashed border-gray-300 hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950 transition-all duration-200"
				>
					Join Sync Session
				</Button>
			</DialogTrigger>
			<DialogContent className="sm:max-w-[450px]">
				<form onSubmit={handleSubmit}>
					<DialogHeader>
						<DialogTitle>Join Sync Session</DialogTitle>
						<DialogDescription>
							Enter the session code to join an existing text sync session. You can get
							this code from another device or share it with others.
						</DialogDescription>
					</DialogHeader>
					<div className="grid gap-6 py-4">
						<div className="grid gap-3">
							<Label htmlFor="room-code" className="text-sm font-medium">
								Session Code
							</Label>
							<Input
								id="room-code"
								name="roomCode"
								placeholder="Enter session code (e.g., ABC123)"
								value={roomCode}
								onChange={(e) => {
									setRoomCode(e.target.value.toUpperCase());
									setError("");
								}}
								className="text-center text-lg font-mono tracking-wider uppercase"
								maxLength={6}
								required
								autoComplete="off"
							/>
							{error && (
								<p className="text-xs text-red-600 dark:text-red-400">
									{error}
								</p>
							)}
							<p className="text-xs text-muted-foreground">
								Session codes are exactly 6 characters long (letters and numbers)
							</p>
						</div>
					</div>
					<DialogFooter>
						<DialogClose asChild>
							<Button variant="outline" disabled={isLoading}>
								Cancel
							</Button>
						</DialogClose>
						<Button
							type="submit"
							disabled={!roomCode.trim() || isLoading || roomCode.length !== 6}
							className="min-w-[100px]"
						>
							{isLoading ? "Joining..." : "Join Session"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
