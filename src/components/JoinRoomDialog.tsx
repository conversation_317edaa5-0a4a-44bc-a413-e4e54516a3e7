import { useState } from "react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";

export function JoinRoomDialog() {
	const [roomCode, setRoomCode] = useState("");
	const [isLoading, setIsLoading] = useState(false);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!roomCode.trim()) return;

		setIsLoading(true);
		// Simulate API call
		await new Promise((resolve) => setTimeout(resolve, 1000));
		setIsLoading(false);

		// Handle room joining logic here
		console.log("Joining room:", roomCode);
	};

	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button
					variant="outline"
					className="w-full border-2 border-dashed border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-all duration-200"
				>
					Join Sync Session
				</Button>
			</DialogTrigger>
			<DialogContent className="sm:max-w-[450px]">
				<form onSubmit={handleSubmit}>
					<DialogHeader>
						<DialogTitle>Join Sync Session</DialogTitle>
						<DialogDescription>
							Enter the session code to join an existing text sync session. You can get
							this code from another device or share it with others.
						</DialogDescription>
					</DialogHeader>
					<div className="grid gap-6 py-4">
						<div className="grid gap-3">
							<Label htmlFor="room-code" className="text-sm font-medium">
								Session Code
							</Label>
							<Input
								name="roomCode"
								placeholder="Enter session code (e.g., ABC123)"
								value={roomCode}
								onChange={(e) => setRoomCode(e.target.value)}
								className="text-center text-lg font-mono tracking-wider uppercase"
								maxLength={6}
								required
							/>
							<p className="text-xs text-muted-foreground">
								Session codes are typically 6 characters long
							</p>
						</div>
					</div>
					<DialogFooter>
						<DialogClose asChild>
							<Button variant="outline" disabled={isLoading}>
								Cancel
							</Button>
						</DialogClose>
						<Button
							type="submit"
							disabled={!roomCode.trim() || isLoading}
							className="min-w-[100px]"
						>
							{isLoading ? "Joining..." : "Join Session"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
