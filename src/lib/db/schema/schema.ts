export * from "./auth.schema";

// export your other schemas here

import { relations } from "drizzle-orm";
import { pgTable, text, timestamp, uuid, varchar } from "drizzle-orm/pg-core";

// --------------------- ROOMS ---------------------
export const rooms = pgTable("rooms", {
	id: uuid("id").defaultRandom().primaryKey(),
	name: varchar("name", { length: 255 }).notNull(),
});

// --------------------- MESSAGE SECTIONS ---------------------
export const messageSection = pgTable("message_sections", {
	id: uuid("id").defaultRandom().primaryKey(),
	roomId: uuid("room_id")
		.notNull()
		.references(() => rooms.id, { onDelete: "cascade" }),
	title: varchar("title", { length: 255 }).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// --------------------- MESSAGES ---------------------
export const messages = pgTable("messages", {
	id: uuid("id").defaultRandom().primaryKey(),
	sectionId: uuid("section_id")
		.notNull()
		.references(() => messageSection.id, { onDelete: "cascade" }),
	content: text("content"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// --------------------- RELATIONS ---------------------
export const roomsRelations = relations(rooms, ({ many }) => ({
	sections: many(messageSection),
}));

export const messageSectionsRelations = relations(
	messageSection,
	({ one, many }) => ({
		room: one(rooms, {
			fields: [messageSection.roomId],
			references: [rooms.id],
		}),
		messages: many(messages),
	}),
);

export const messagesRelations = relations(messages, ({ one }) => ({
	section: one(messageSection, {
		fields: [messages.sectionId],
		references: [messageSection.id],
	}),
}));
